<template>
  <!-- 优势 -->
  <div class="features-section">
    <!-- 标题 -->
    <div class="section-title q-mb-md">
      <h2>{{ $t('ourAdvantages') }}</h2>
    </div>

    <!-- Tabs for manual selection - 桌面端 -->
    <div class="q-mb-md gt-xs">
      <q-tabs v-model="activeTab" align="justify" dense @update:model-value="onTabClick" class="feature-tabs">
        <q-tab v-for="(tab, index) in features" :key="index" :name="index" :label="tab" />
      </q-tabs>
    </div>

    <!-- 移动端标签选择器 -->
    <div class="q-mb-md lt-sm">
      <!-- <q-tabs v-model="activeTab" align="justify" dense @update:model-value="onTabClick" class="feature-tabs">
        <q-tab v-for="(tab, index) in features" :key="index" :name="index" :label="tab" />
      </q-tabs> -->
    </div>

    <!-- QCarousel for slides -->
    <q-carousel
      v-model="currentSlide"
      swipeable
      infinite
      transition-prev="slide-right"
      transition-next="slide-left"
      @update:model-value="onSlideChange"
      navigation
      arrows
      height="auto"
      class="feature-carousel q-mb-xl">
      <q-carousel-slide v-for="(slide, index) in slides" :key="index" :name="index" class="feature-slide">
        <q-img :src="slide.img" :ratio="16 / 9" class="feature-image" />
        <!-- <div class="feature-label q-mt-md">
          {{ slide.label }}
        </div> -->
      </q-carousel-slide>
    </q-carousel>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n';
import { computed, ref, onMounted, onUnmounted } from 'vue';
const { tm, t } = useI18n(); // 获取i18n中的locale

const features = computed(() => tm('features'));

// 为移动端下拉框创建选项
const featureOptions = computed(() => {
  return features.value.map((feature, index) => ({
    label: feature,
    value: index,
  }));
});

// 固定图片列表
const slides = computed(() => {
  return features.value.map((feature, index) => ({
    img: `images/ys${index + 1}.png`,
    label: feature,
  }));
});

// Active states
const currentSlide = ref(0); // QCarousel's current slide index
const activeTab = ref(0); // Current active tab index
const mobileActiveTab = ref(0); // Mobile dropdown active tab

// Auto-play logic
let intervalId = null;
const autoPlay = () => {
  intervalId = setInterval(() => {
    currentSlide.value = (currentSlide.value + 1) % slides.value.length;
    activeTab.value = currentSlide.value; // Sync tab with slide
    mobileActiveTab.value = currentSlide.value; // Sync mobile dropdown with slide
  }, 5000); // 5 seconds interval
};

// Clean up interval on unmount
onMounted(autoPlay);
onUnmounted(() => {
  if (intervalId) clearInterval(intervalId);
});

// Handle tab click to switch slides
const onTabClick = (tabIndex) => {
  currentSlide.value = tabIndex; // Sync slide with tab
  mobileActiveTab.value = tabIndex; // Sync mobile dropdown with tab
};

// Handle slide change to sync with tab
const onSlideChange = (slideIndex) => {
  activeTab.value = slideIndex; // Sync desktop tab with slide
  mobileActiveTab.value = slideIndex; // Sync mobile dropdown with slide
};
</script>

<style lang="scss" scoped>
//优势
.features-section {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
  margin-top: 50px;
  text-align: center;

  .section-title {
    h2 {
      font-size: 32px;
      font-weight: bold;
      margin-bottom: 20px;
    }
  }

  .feature-tabs {
    .q-tab {
      font-size: 20px;
      font-weight: 500;

      &--active {
        font-weight: bold;
      }
    }
  }

  .feature-carousel {
    border-radius: 8px;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
    overflow: hidden;
  }

  .feature-slide {
    padding: 0;
  }

  .feature-image {
    width: 100%;
    max-height: 600px;
    object-fit: cover;
  }

  .feature-label {
    font-size: 18px;
    font-weight: 500;
    padding: 16px;
  }
}

@media (max-width: 599px) {
  .features-section {
    margin-top: 30px;

    .section-title {
      h2 {
        font-size: 24px;
      }
    }

    .feature-carousel {
      max-height: 400px;
    }

    .feature-label {
      font-size: 16px;
      padding: 12px;
    }
  }
}

@media (min-width: 600px) and (max-width: 1023px) {
  .features-section {
    margin-top: 40px;

    .feature-carousel {
      max-height: 500px;
    }
  }
}
</style>
