<template>
  <!-- 桌面端侧边栏 -->
  <div class="shadow-box fixed-bar show-fixed gt-xs">
    <div class="shadow-item flex" @click="navigateTo('/pack')">
      <i class="iconfont icon-libao fixed-icon"></i>
      <!-- <div class="shadow-label">{{ $t('pack') }}</div> -->
    </div>

    <div class="shadow-item flex" @click="navigateTo('/account/msg-inbox')">
      <div class="icon-container">
        <i class="iconfont icon-tixing fixed-icon"></i>
        <q-badge v-if="unreadCount > 0" :label="unreadCount > 99 ? '99+' : unreadCount" color="red" floating rounded />
      </div>
      <!-- <div class="shadow-label">{{ $t('message') }}</div> -->
    </div>
    <div class="shadow-item flex" @click="navigateTo('/account/wishlist')">
      <i class="iconfont icon-yichoucang fixed-icon"></i>
      <!-- <div class="shadow-label">{{ $t('favorite') }}</div> -->
    </div>
    <div class="shadow-item flex" @click="openWindow('https://discord.gg/zdsmAZE3')">
      <i class="iconfont icon-discord fixed-icon"></i>
      <!-- <div class="shadow-label">{{ $t('cart') }}</div> -->
    </div>
    <div class="shadow-item flex" @click="scrollToTop">
      <i class="iconfont icon-arrowupl fixed-icon"></i>
      <!-- <div class="shadow-label">{{ $t('top') }}</div> -->
    </div>
  </div>

  <!-- 移动端底部导航栏 -->
  <div class="mobile-nav lt-sm">
    <div class="mobile-nav-item" @click="navigateTo('/')">
      <q-icon name="home" size="24px" />
      <div class="mobile-nav-label">{{ $t('menu.home') }}</div>
    </div>
    <div class="mobile-nav-item" @click="navigateTo('/cart')">
      <q-icon name="shopping_cart" size="24px" />
      <div class="mobile-nav-label">{{ $t('cart') }}</div>
    </div>
    <div class="mobile-nav-item" @click="navigateTo('/account/wishlist')">
      <q-icon name="favorite" size="24px" />
      <div class="mobile-nav-label">{{ $t('favorite') }}</div>
    </div>
    <div class="mobile-nav-item" @click="navigateTo('/account')">
      <q-icon name="person" size="24px" />
      <div class="mobile-nav-label">{{ $t('button.myCenter') }}</div>
    </div>
  </div>

  <!-- 移动端回到顶部按钮 -->
  <div class="back-to-top-btn lt-sm" v-show="showBackToTop">
    <q-btn round color="primary" icon="keyboard_arrow_up" @click="scrollToTop" />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { useAuthStore } from '~/store/auth';
import NotifyApi from '~/composables/notifyApi';

const router = useRouter();
const { t } = useI18n(); // 获取i18n中的locale
const authStore = useAuthStore();
const showBackToTop = ref(false);
const unreadCount = ref(0);
let unreadCountTimer = null;

// 导航到指定路径
const navigateTo = (path) => {
  router.push(path);
};

const openWindow = (url) => {
  window.open(url, '_blank');
};

// 平滑滚动到顶部
const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth',
  });
};

// 获取未读消息数量
const fetchUnreadCount = async () => {
  if (!authStore.isLogin) {
    unreadCount.value = 0;
    return;
  }

  try {
    const { code, data } = await NotifyApi.getUnreadCount();
    if (code === 0) {
      unreadCount.value = data || 0;
    }
  } catch (error) {
    console.error('获取未读消息数量失败:', error);
  }
};

// 启动定时获取未读消息数量
const startUnreadCountTimer = () => {
  // 立即获取一次
  fetchUnreadCount();

  // 每30秒获取一次
  unreadCountTimer = setInterval(() => {
    fetchUnreadCount();
  }, 3000000); //todo 临时测试，后面改成30秒
};

// 停止定时器
const stopUnreadCountTimer = () => {
  if (unreadCountTimer) {
    clearInterval(unreadCountTimer);
    unreadCountTimer = null;
  }
};

// 监听滚动事件，控制回到顶部按钮的显示
const handleScroll = () => {
  showBackToTop.value = window.scrollY > 300;
};

onMounted(() => {
  window.addEventListener('scroll', handleScroll);
  startUnreadCountTimer();
});

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll);
  stopUnreadCountTimer();
});
</script>

<style scoped lang="scss">
// 桌面端侧边栏
.shadow-box {
  width: 70px;
  border-radius: 8px;
  background: #ffffff;
  box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.1);
  position: fixed;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 999;

  .shadow-item {
    padding: 10px 0;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid #eee;
    transition: background-color 0.3s;

    &:hover {
      background-color: #f5f5f5;
    }

    &:last-child {
      border-bottom: none;
    }

    .icon-container {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .fixed-icon {
      font-size: 26px;
      color: #666;
      text-align: center;
    }

    .shadow-label {
      font-size: 12px;
      font-weight: normal;
      line-height: 1.2;
      text-align: center;
      margin-top: 4px;
      color: #666;
    }
  }
}

// 移动端底部导航栏
.mobile-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 56px;
  background-color: #fff;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-around;
  align-items: center;
  z-index: 999;

  .mobile-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    height: 100%;
    cursor: pointer;
    padding: 8px 0;

    .mobile-nav-label {
      font-size: 12px;
      margin-top: 4px;
      color: #666;
    }
  }
}

// 显示/隐藏动画
.fixed-bar {
  transition: opacity 0.3s, transform 0.3s;
}

.show-fixed {
  opacity: 1 !important;
}

// 响应式调整
@media screen and (min-width: 1921px) {
  .shadow-box {
    right: 40px;
  }
}

@media screen and (max-width: 1440px) {
  .shadow-box {
    right: 15px;
  }
}

.flex {
  display: flex;
}

// 移动端回到顶部按钮
.back-to-top-btn {
  position: fixed;
  bottom: 16px;
  right: 16px;
  z-index: 999;
}
</style>
