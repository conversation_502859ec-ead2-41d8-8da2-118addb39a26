<template>
  <!-- 重定向页面：将 /product/[id] 重定向到统一的 /product 页面 -->
  <div class="redirect-container">
    <q-spinner color="primary" size="3em" />
    <p>正在跳转...</p>
  </div>
</template>

<script setup>
import { useRoute, useRouter } from "vue-router";

const route = useRoute();
const router = useRouter();

// 立即重定向到统一的商品页面
const productId = route.params.id;
if (productId) {
  // 重定向到 /product 页面，保持原有的 ID 参数
  await router.replace(`/product?id=${productId}&source=database`);
} else {
  // 如果没有 ID，重定向到首页
  await router.replace("/");
}
</script>

<style scoped>
.redirect-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  text-align: center;
}
</style>
