<template>
  <Header />
  <Breadcrumbs :breadcrumbs="breadcrumbs" />

  <div class="product-detail q-pt-lg">
    <!-- 加载状态 -->
    <div v-if="isLoading || searchStore.detailLoading" class="loading-container">
      <q-spinner color="primary" size="3em" />
      <p>正在获取商品信息...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="hasError || (!searchStore.productDetail && !searchStore.detailLoading && !isDirectAccess) || (!product.title && !isLoading && isDirectAccess)" class="error-container">
      <q-icon name="error_outline" size="60px" color="grey-5" />
      <h3>无法获取商品信息</h3>
      <p>{{ errorMessage || '请检查商品链接是否正确，或稍后重试' }}</p>

      <div class="error-actions">
        <q-btn color="primary" @click="retrySearch" class="q-mr-sm">重试</q-btn>
        <q-btn flat color="grey" @click="goBack" class="q-mr-sm">返回</q-btn>
      </div>

      <!-- 自定义订单建议 -->
      <div class="custom-order-suggestion q-mt-lg">
        <q-card flat bordered class="suggestion-card">
          <q-card-section class="text-center">
            <q-icon name="build" size="32px" color="primary" class="q-mb-sm" />
            <h4 class="q-mb-sm">无法获取商品信息？</h4>
            <p class="q-mb-md">创建自定义订单，提供商品链接和详细信息，我们帮您代购！</p>
            <q-btn color="primary" @click="goToCustomOrderWithCurrentLink" icon="build"> 创建自定义订单 </q-btn>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <!-- 商品详情内容 -->
    <div v-else-if="product && product.title" class="product-detail-content">
      <!-- 主体布局 -->
      <div class="row q-col-gutter-md">
        <!-- 左侧：商品图片展示区 -->
        <div class="col-12 col-md-6 col-lg-5">
          <!-- 大图轮播 -->
          <div class="carousel-container" :class="{ 'mobile-carousel-container': isMobile }">
            <q-carousel
              v-model="currentSlide"
              transition-prev="slide-right"
              transition-next="slide-left"
              animated
              :arrows="!isMobile"
              swipeable
              :height="isMobile ? 'auto' : '500px'"
              class="rounded-borders product-main-image"
              :class="{ 'mobile-carousel': isMobile }">
              <q-carousel-slide v-for="(image, index) in productImages" :key="index" :name="index" class="column items-center justify-center q-pa-sm" :class="{ 'mobile-carousel-slide': isMobile }">
                <q-img :src="image.src" :alt="image.alt" class="full-width" :fit="isMobile ? 'cover' : 'contain'" :class="{ 'mobile-carousel-img': isMobile }" />
              </q-carousel-slide>
            </q-carousel>

            <!-- 移动端轮播按钮 -->
            <template v-if="isMobile">
              <q-btn round flat dense color="white" icon="chevron_left" class="mobile-carousel-nav mobile-carousel-nav-left" @click="prevSlide" />
              <q-btn round flat dense color="white" icon="chevron_right" class="mobile-carousel-nav mobile-carousel-nav-right" @click="nextSlide" />
            </template>
          </div>

          <!-- 小图预览区域 - 桌面端和平板端 -->
          <div class="thumbnail-carousel q-mt-sm gt-xs">
            <q-carousel v-model="thumbnailSlide" control-color="primary" arrows swipeable animated height="110px" class="bg-grey-1">
              <!-- 每组5个小图 -->
              <q-carousel-slide v-for="(group, index) in groupedThumbnails" :key="index" :name="index" class="row no-wrap q-px-md" :class="{ 'justify-start': index === groupedThumbnails.length - 1 }">
                <q-img
                  v-for="(image, idx) in group"
                  :key="idx"
                  :src="image.src"
                  :alt="image.alt"
                  class="thumbnail-item rounded-borders"
                  :class="{ active: currentSlide === index * 5 + idx }"
                  @click="currentSlide = index * 5 + idx" />
              </q-carousel-slide>
            </q-carousel>
          </div>

          <!-- 小图预览区域 - 移动端 -->
          <div class="thumbnail-carousel-mobile q-mt-sm lt-sm">
            <div class="row justify-center q-gutter-sm">
              <q-img
                v-for="(image, index) in productImages.slice(0, 5)"
                :key="index"
                :src="image.src"
                :alt="image.alt"
                class="thumbnail-item-mobile rounded-borders"
                :class="{ active: currentSlide === index }"
                @click="currentSlide = index" />
            </div>
          </div>
        </div>

        <!-- 右侧：商品信息 -->
        <div class="col-12 col-md-6 col-lg-7 q-pa-md">
          <h1 class="product-title q-mb-md">{{ product.title }}</h1>

          <!-- 店铺信息 -->
          <div class="row justify-between q-mt-sm">
            <div>
              <!-- <q-icon name="store" color="primary" size="20px" /> -->
              <q-icon :name="`svguse:icons/${product.source}.svg|0 0 80 80`" size="24px" class="q-mr-sm" />
              <span class="q-ml-sm">{{ product.shopName }}</span>
            </div>
            <a v-if="product.type === 1" :href="product.sourceLink" target="_blank" class="text-primary">{{ $t('products.detail.productLink') }}</a>
          </div>

          <!-- 价格行 -->
          <div class="price-section q-mt-sm q-py-sm rounded-borders">
            <div class="row items-center">
              <div class="col-12 col-sm-2 spec-label">{{ $t('products.detail.price') }}:</div>
              <div class="col-12 col-sm-10 row items-center justify-center q-mt-xs q-mt-sm-none" style="min-height: 32px">
                <span class="text-red text-bold text-h5" v-html="formatAmount(state.selectedSku.price || product.price, { allowWrap: false, useHtml: false })"></span>
                <q-btn v-if="!showPriceInput" flat dense icon="edit" color="primary" class="q-ml-sm q-px-xs" @click="showPriceInput = true" />
                <!-- 输入框和按钮 -->
                <div v-if="showPriceInput" class="row items-center q-mt-xs q-mt-sm-none">
                  <q-input v-model="formattedCartPrice" dense outlined type="number" class="q-ml-sm q-pa-none" style="width: 80px" />
                  <q-btn color="primary" dense flat :label="$t('products.detail.save')" class="q-ml-xs" @click="savePrice" />
                  <q-btn color="grey" dense flat :label="$t('products.detail.cancel')" class="q-ml-xs" @click="cancelPrice" />
                </div>
                <!-- 问号提示 -->
                <q-icon name="help_outline" size="16px" class="q-ml-xs cursor-pointer text-primary">
                  <q-tooltip anchor="top middle" self="bottom middle" :offset="[10, 10]">
                    <div>{{ $t('products.detail.priceEditTip') }}</div>
                  </q-tooltip>
                </q-icon>
              </div>
            </div>
          </div>

          <!-- 商品信息和规格 -->
          <div class="product-info-container q-mt-md">
            <!-- 基本信息行 -->
            <div class="row q-col-gutter-sm mobile-center">
              <div class="col-6 col-sm-4">
                <div class="info-chip">
                  <q-icon name="inventory_2" size="xs" class="q-mr-xs" />
                  <span class="label-text q-mr-xs">{{ $t('products.detail.stock') }}:</span>
                  <span class="value-text">{{ state.selectedSku.stock || product.stock }}</span>
                </div>
              </div>
              <div class="col-6 col-sm-4">
                <div class="info-chip">
                  <q-icon name="local_shipping" size="xs" class="q-mr-xs" />
                  <span class="label-text q-mr-xs">{{ $t('products.detail.freight') }}:</span>
                  <span class="value-text">{{ (product.freight / 100).toFixed(2) }} RMB</span>
                </div>
              </div>
              <div class="col-12 col-sm-4">
                <div class="size-guide-container text-right">
                  <q-btn flat dense color="primary" icon="straighten" :label="$t('sizeGuide.title')" @click="showSizeGuideDialog = true" class="size-guide-btn" />
                </div>
              </div>
            </div>

            <!-- 商品规格行 -->
            <div class="specs-section q-mt-md">
              <!-- 优化的规格属性布局 -->
              <div v-for="property in product.propertyList" :key="property.id" class="spec-item q-mb-md">
                <div class="row no-wrap items-center q-mb-sm mobile-center">
                  <div class="spec-label">{{ property.name }}:</div>
                  <q-icon v-if="getPropertyHint(property.name)" name="help_outline" size="16px" color="grey-6" class="q-ml-xs cursor-pointer">
                    <q-tooltip class="bg-grey-8 text-white" :offset="[10, 10]">
                      {{ getPropertyHint(property.name) }}
                    </q-tooltip>
                  </q-icon>
                </div>
                <div class="spec-values">
                  <div class="attribute-grid" :class="getGridClass(property.values.length)">
                    <q-btn
                      v-for="(value, index) in property.values"
                      :key="index"
                      :label="value.name"
                      :color="chooseState[property.id] === value.id ? 'primary' : 'grey-3'"
                      :text-color="chooseState[property.id] === value.id ? 'white' : 'black'"
                      unelevated
                      dense
                      :disable="value.disabled"
                      class="attribute-btn"
                      :class="{
                        disable: value.disabled,
                        'selected-attribute': chooseState[property.id] === value.id,
                        'long-text': value.name.length > 10,
                      }"
                      @click="onSelectSku(property.id, value.id)">
                      <q-tooltip v-if="value.name.length > 15" class="bg-grey-8 text-white">
                        {{ value.name }}
                      </q-tooltip>
                    </q-btn>
                  </div>
                </div>
              </div>

              <!-- 购买数量 -->
              <div class="spec-item q-mb-sm">
                <div class="row no-wrap items-center q-mb-xs mobile-center">
                  <div class="spec-label">{{ $t('products.detail.quantity') }}:</div>
                </div>
                <div class="quantity-selector mobile-center">
                  <q-btn flat round dense color="primary" icon="remove" @click="decreaseQuantity" :disable="quantity <= 1" />
                  <q-input
                    v-model.number="quantity"
                    dense
                    outlined
                    input-style="text-align: center"
                    class="quantity-input"
                    maxlength="4"
                    @update:model-value="checkQuantity"
                    oninput="value=value.replace(/[^0-9.]/g,'')" />
                  <q-btn flat round dense color="primary" icon="add" @click="increaseQuantity" />
                </div>
              </div>

              <!-- 备注 -->
              <div class="spec-item q-mb-sm">
                <div class="row no-wrap items-center q-mb-xs mobile-center">
                  <div class="spec-label">{{ $t('products.detail.memo') }}:</div>
                </div>
                <div class="memo-container">
                  <q-input v-model="memo" outlined dense type="textarea" maxlength="1024" class="memo-input" rows="3" />
                </div>
              </div>
            </div>
          </div>

          <!-- 按钮组 -->
          <div class="action-buttons q-mt-md">
            <div class="row q-col-gutter-sm">
              <div class="col-12 col-sm-2 gt-xs"></div>
              <div class="col-12 col-sm-10">
                <div class="row q-col-gutter-sm justify-center justify-sm-start">
                  <div class="col-6 col-sm-auto">
                    <q-btn color="primary" class="full-width action-btn cart-btn" @click="addToCart">
                      <q-icon name="shopping_cart" class="q-mr-xs" size="xs" />
                      {{ $t('products.detail.addToCart') }}
                    </q-btn>
                  </div>
                  <div class="col-6 col-sm-auto">
                    <q-btn color="deep-orange" class="full-width action-btn buy-btn" @click="buyNow">
                      <q-icon name="payments" class="q-mr-xs" size="xs" />
                      {{ $t('products.detail.buyNow') }}
                    </q-btn>
                  </div>
                  <div class="col-12 col-sm-auto">
                    <q-btn color="grey-7" flat class="full-width action-btn wishlist-btn q-mt-xs q-mt-sm-none" @click="toggleWishlist">
                      <q-icon name="favorite" :icon="isFavorited ? 'favorite' : 'favorite_border'" :color="isFavorited ? 'red' : 'grey'" class="q-mr-xs" size="xs" />
                      {{ isFavorited ? '取消收藏' : '收藏' }}
                    </q-btn>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- TAB 切换区域 -->
      <div class="product-tabs q-mt-xl">
        <q-tabs v-model="tab" align="left" class="text-primary" :breakpoint="0" dense :mobile-arrows="isMobile">
          <q-tab name="description" :label="$t('products.detail.description')" />
          <q-tab name="purchaseNotes" :label="$t('products.detail.purchaseNotes')" />
          <q-tab name="afterSale" :label="$t('products.detail.afterSale')" />
        </q-tabs>

        <q-separator />

        <q-tab-panels v-model="tab" animated class="bg-white">
          <q-tab-panel name="description">
            <div class="text-body1 product-description">
              <div v-html="product.description"></div>
            </div>
          </q-tab-panel>

          <q-tab-panel name="purchaseNotes">
            <div class="text-body1">
              <p>{{ $t('products.detail.purchaseNotesContent') }}</p>
            </div>
          </q-tab-panel>

          <q-tab-panel name="afterSale">
            <div class="text-body1">
              <p>{{ $t('products.detail.afterSaleContent') }}</p>
            </div>
          </q-tab-panel>
        </q-tab-panels>
      </div>
    </div>
  </div>

  <!-- 尺码助手弹窗 -->
  <q-dialog v-model="showSizeGuideDialog" :maximized="isMobile">
    <q-card class="size-guide-dialog" :style="dialogStyle">
      <q-card-section class="row items-center q-pb-none">
        <div class="text-h6">{{ $t('sizeGuide.title') }}</div>
        <q-space />
        <q-btn icon="close" flat round dense v-close-popup />
      </q-card-section>

      <q-card-section class="q-pt-none size-guide-content-wrapper">
        <SizeGuideContent :default-category="getSizeGuideCategory()" />
      </q-card-section>
    </q-card>
  </q-dialog>

  <Footer />
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useCartStore } from '~~/store/cart';
import { useFavoriteStore } from '~/store/favorite';
import { useSearchStore } from '~/store/search';
import { useOrderConfirmStore } from '~/store/orderConfirm';
import { useI18n } from 'vue-i18n';
import { useResponsive } from '~/composables/useResponsive';
import { useCurrency } from '~/composables/useCurrency';
import ProductApi from '~/composables/productApi';
import FavoriteApi from '~/composables/favoriteApi';
import SizeGuideContent from '~/components/SizeGuideContent.vue';
import SearchApi from '~/composables/searchApi';

const cartStore = useCartStore();

const favoriteStore = useFavoriteStore();
const orderConfirmStore = useOrderConfirmStore();
const router = useRouter();
const route = useRoute();
const searchStore = useSearchStore();
const { isMobile } = useResponsive();
const { formatAmount } = useCurrency();
const { t } = useI18n();

// 支持两种路由方式：
// 1. /product?id=xxx&platform=xxx 或 /product?url=xxx (搜索页面跳转)
// 2. /product?id=xxx&source=database (数据库商品重定向访问)
const id = route.query.id;
const platform = route.query.platform;
const productUrl = route.query.url;
const source = route.query.source;
const isDirectAccess = source === 'database'; // 判断是否为数据库商品访问

// 统一的加载和错误状态管理
const isLoading = ref(false);
const hasError = ref(false);
const errorMessage = ref('');

// 面包屑导航
const breadcrumbs = [{ label: t('products.detail.productDetail'), to: route.fullPath }];

let product = reactive({
  id: '',
  title: '',
  description: '',
  type: 1,
  price: 0,
  cartPrice: 0,
  stock: 0,
  freight: 0,
  shopName: '',
  source: '',
  sourceId: '',
  sourceLink: '',
  images: [],
  skus: [],
  propertyList: [],
}); // 存储商品详情
// 选中的状态：以属性 ID 作为 key
const chooseState = reactive({});

const state = reactive({
  selectedSku: {}, // 选中的 SKU
  currentPropertyArray: [], // 当前选中的属性，实际是个 Map。key 是 property 编号，value 是 value 编号
});

//购买数量
const quantity = ref(1);
//备注
const memo = ref('');
// 尺码助手弹窗
const showSizeGuideDialog = ref(false);

// 弹窗样式计算属性
const dialogStyle = computed(() => {
  if (isMobile.value) {
    return {}; // 移动端使用maximized，不需要额外样式
  }
  return {
    width: '85vw',
    maxWidth: '1000px',
    height: '75vh',
    maxHeight: '700px',
    minHeight: '500px',
  };
});

// 轮播图相关
const currentSlide = ref(0);
// 当前小图轮播的索引
const thumbnailSlide = ref(0);

// 计算商品图片数组
const productImages = computed(() => {
  if (!product) return [];
  return product.images || [];
});

// 每5个小图分组
const groupedThumbnails = computed(() => {
  const groups = [];
  for (let i = 0; i < productImages.value.length; i += 5) {
    groups.push(productImages.value.slice(i, i + 5));
  }
  return groups;
});

// SKU 列表
const skuList = computed(() => {
  if (!product || !product.skus) return [];
  const skus = product.skus;
  for (const sku of skus) {
    sku.value_id_array = sku.properties.map((item) => item.valueId);
  }
  return skus;
});

const cartPrice = ref(0);
const showPriceInput = ref(false);

// 计算属性，用于显示和更新
const formattedCartPrice = computed({
  get() {
    // 显示为元
    return (cartPrice.value / 100).toFixed(2);
  },
  set(newValue) {
    // 转回分
    cartPrice.value = Math.round(parseFloat(newValue) * 100);
  },
});

// 轮播控制函数 - 用于移动端自定义导航按钮
const prevSlide = () => {
  if (currentSlide.value > 0) {
    currentSlide.value--;
  } else {
    currentSlide.value = productImages.value.length - 1;
  }
};

const nextSlide = () => {
  if (currentSlide.value < productImages.value.length - 1) {
    currentSlide.value++;
  } else {
    currentSlide.value = 0;
  }
};

// TAB 切换
const tab = ref('description');

// 页面初始化
onMounted(async () => {
  // 开始加载
  isLoading.value = true;
  hasError.value = false;
  errorMessage.value = '';

  try {
    // 判断访问方式并获取数据
    if (isDirectAccess) {
      // 直接访问数据库商品 (路由: /product?id=xxx&source=database)
      console.log('直接访问数据库商品，ID:', id);

      const { code, data } = await ProductApi.getSpuDetail(id);
      if (code === 0) {
        // 使用ProductApi的转换方法
        const convertedProduct = ProductApi.convertBackendProductToTemplate(data);
        Object.assign(product, convertedProduct);
        console.log('数据库商品数据获取成功:', product.title);
        cartPrice.value = product.price;
      } else {
        console.error('获取数据库商品失败，错误码:', code);
        hasError.value = true;
        errorMessage.value = '获取商品信息失败，请稍后重试';
      }
    } else {
      // 搜索页面跳转 (路由: /product?id=xxx&platform=xxx 或 /product?url=xxx)
      if (id == null || platform == null) {
        console.log('id or platform is null');
      }

      // 执行商品详情搜索
      if (productUrl) {
        await searchStore.searchProductDetailByUrl(productUrl);
      } else {
        await searchStore.searchProductDetailById(id, platform);
      }

      // 转换数据格式
      if (searchStore.productDetail) {
        const convertedProduct = ProductApi.convertBackendProductToTemplate(searchStore.productDetail);
        Object.assign(product, convertedProduct);
        console.log('转换后的产品信息:', product);
        cartPrice.value = product.price;
      } else {
        hasError.value = true;
        errorMessage.value = '无法获取商品信息，请检查链接是否正确';
      }
    }
  } catch (error) {
    console.error('获取商品数据时发生错误:', error);
    hasError.value = true;
    errorMessage.value = '获取商品信息时发生错误，请稍后重试';
  } finally {
    // 结束加载
    isLoading.value = false;
  }

  // 如果成功获取到商品数据，执行后续操作
  if (product && product.id) {
    // 检查是否收藏过商品
    try {
      const { code, data } = await FavoriteApi.isFavoriteExists(product.id);
      if (code === 0) {
        isFavorited.value = data;
      }
    } catch (error) {
      console.error('检查收藏状态时发生错误:', error);
    }

    // 获取商品详情描述（仅对搜索来的商品）
    if (!isDirectAccess && (!product.description || product.description.trim() === '')) {
      console.log('获取商品详情，当前描述:', product.description);
      try {
        const { code, data } = await SearchApi.getProductDescription(product.source, product.sourceId);
        if (code === 0 && data) {
          // 直接设置响应式对象的属性
          product.description = data;
          console.log('商品详情获取成功，已更新到product对象:', data.substring(0, 100) + '...');
        } else {
          console.log('商品详情获取失败，错误码:', code);
        }
      } catch (error) {
        console.error('获取商品详情时发生错误:', error);
      }
      console.log('商品详情处理结束，当前描述长度:', product.description?.length || 0);
    } else if (product.description) {
      console.log('商品已有描述，跳过获取:', product.description.substring(0, 100) + '...');
    }
  }
});

const savePrice = () => {
  state.selectedSku.price = cartPrice.value;
  showPriceInput.value = false;
};

const cancelPrice = () => {
  cartPrice.value = product.price;
  showPriceInput.value = false;
};

// 减少数量
const decreaseQuantity = () => {
  if (quantity.value > 1) {
    quantity.value -= 1;
  }
};

// 增加数量
const increaseQuantity = () => {
  const stock = state.selectedSku.stock || product.stock;
  if (quantity.value >= stock) return;
  quantity.value += 1;
};

// 检查输入数量是否合法
const checkQuantity = () => {
  const stock = state.selectedSku.stock || product.stock;
  if (quantity.value > stock) {
    quantity.value = stock; // 超过库存自动修正为库存数量
  } else if (quantity.value < 1 || !quantity.value) {
    quantity.value = 1; // 小于 1 修正为 1
  }
};

// 选择规格
const onSelectSku = (propertyId, valueId) => {
  // 判断当前值是否已选中，如果选中则取消
  if (chooseState[propertyId] === valueId) {
    // 使用Vue的响应式API删除属性
    chooseState[propertyId] = undefined;
  } else {
    chooseState[propertyId] = valueId;
  }
  // 重新计算禁用状态和选中的 SKU
  updateDisabledStates();
  updateSelectedSku();
};

// 更新禁用状态
const updateDisabledStates = () => {
  if (!product.propertyList) return;
  product.propertyList.forEach((property) => {
    property.values.forEach((value) => {
      // 构建一个临时选择状态，将当前属性值替换进去
      const tempSelected = { ...chooseState, [property.id]: value.id };
      // 判断当前组合是否存在于 SKU 列表，且有库存
      const matchedSku = skuList.value.find((sku) => sku.stock > 0 && Object.values(tempSelected).every((id) => sku.value_id_array.includes(id)));
      value.disabled = !matchedSku; // 不匹配则禁用
    });
  });
};

// 更新选中的 SKU
const updateSelectedSku = () => {
  if (!product.propertyList) return;
  const selectedValues = Object.values(chooseState);
  // 如果没有完全选中所有属性，清空选中的 SKU
  if (selectedValues.length < product.propertyList.length) {
    state.selectedSku = {}; // 清空 selectedSku
    return;
  }
  // 查找符合当前属性组合的 SKU
  state.selectedSku = skuList.value.find((sku) => sku.stock > 0 && selectedValues.every((id) => sku.value_id_array.includes(id))) || {};
  //切换到选中sku的图片
  const imageItems = product.images.filter((item) => item.variant_id === state.selectedSku.id);
  if (imageItems && imageItems.length > 0 && imageItems[0].image_id !== undefined) {
    currentSlide.value = imageItems[0].image_id;
  }
  //纠正购买数量要小于库存数量
  if (state.selectedSku.stock < quantity.value) {
    quantity.value = state.selectedSku.stock;
  }
  //金额更新为所选金额
  cartPrice.value = state.selectedSku.price;
};

const addToCart = async () => {
  console.log('addToCart');
  //检查选中
  if (!checkSubmit()) {
    return;
  }

  const cartItem = await prepareSubmitData();

  console.log('addToCart1转换后的商品:', cartItem);
  console.log('转换前选定的商品SKU:', state.selectedSku);
  //补充必要信息
  cartItem.price = cartPrice.value || product.price;
  cartItem.memo = memo.value || '';
  cartItem.type = 1;
  console.log('补充后的商品:', cartItem);

  // 代购商品添加到购物车
  // const cartItem = {
  //   id: 0,
  //   type: 1, //代购三方平台商品
  //   count: quantity.value || 1,
  //   selected: true,
  //   storeName: product.shopName || 'Shop Name',
  //   price: cartPrice.value || product.price,
  //   memo: memo.value || '',
  //   spu: {
  //     id: product.id,
  //     name: product.title,
  //     picUrl: product.images[0]?.src || '',
  //     categoryId: product.categoryId,
  //     freight: product.freight,
  //     source: product.source,
  //   },
  //   sku: {
  //     id: state.selectedSku.id, // 代购商品使用商品ID作为SKU ID
  //     picUrl: product.images[0]?.src || '',
  //     price: product.price,
  //     stock: product.stock,
  //     properties: [],
  //   },
  // };
  // console.log('addToCart2', cartItem);
  try {
    // 调用 API
    await cartStore.addAgentProduct(cartItem);
    useNuxtApp().$showNotify({ msg: t('products.detail.addedToCart') });
    // 跳转到购物车页面
    router.push('/cart');
  } catch (error) {
    console.error('Failed to add to cart:', error);
    useNuxtApp().$showNotify({ msg: t('products.detail.addToCartFailed'), type: 'negative' });
  }
};

// 立即购买
const buyNow = async () => {
  if (!checkSubmit()) {
    return;
  }
  //trackBuyNow(); 数据跟踪
  //把已选的购物车ID存储到订单确认页的store
  const cartItem = await prepareSubmitData();
  cartItem.id = -1; //防止传0被拦截
  orderConfirmStore.source = 'direct';
  orderConfirmStore.directItem = cartItem;
  // 跳转到订单确认页面
  navigateTo('/order/confirm');
};

const checkSubmit = () => {
  if (!state.selectedSku || Object.keys(state.selectedSku).length === 0) {
    useNuxtApp().$showNotify({ msg: '请选择商品规格', type: 'error' });
    return false;
  }
  if (state.selectedSku.stock <= 0 || state.selectedSku.stock < quantity.value) {
    useNuxtApp().$showNotify({ msg: '库存不足', type: 'error' });
    return false;
  }
  return true;
};

//把商品信息传递到后端转换为本地商品
const prepareSubmitData = async () => {
  const params = {
    source: product.source,
    sourceId: product.sourceId,
    sourceSkuId: state.selectedSku.sourceSkuId,
  };

  const { code, data } = await ProductApi.productTransition(params);
  console.log('转换后的商品信息：', data);
  if (code === 0) {
    return {
      id: 0,
      count: quantity.value || 1,
      selected: true,
      memo: memo.value || '',
      spu: {
        id: data.id,
        name: data.name,
        picUrl: data.picUrl,
        categoryId: data.categoryId,
        price: state.selectedSku.price,
        shopName: data.shopName,
        freight: data.freight,
        sorce: data.source,
      },
      sku: {
        id: data.skus[0]?.id ?? 0,
        picUrl: data.skus[0]?.picUrl ?? data.picUrl ?? '',
        price: data.skus[0]?.price ?? 0,
        stock: data.skus[0]?.stock ?? 0,
        properties: data.skus[0]?.properties ?? [],
      },
    };
  }
};

//收藏状态
const isFavorited = ref(false);
const localProductSpuId = ref(null);

// 切换收藏状态
const toggleWishlist = () => {
  if (isFavorited.value) {
    // 执行从收藏移除逻辑
    console.log('执行从收藏移除逻辑');
    removeFromWishlist();
  } else {
    // 执行添加到收藏逻辑
    console.log('添加到收藏夹');
    addToWishlist();
  }
};

// 添加到收藏夹
async function addToWishlist() {
  console.log('添加到收藏夹');
  try {
    //把代购平台商品专为本地商品
    const localItem = await prepareSubmitData();
    if (localItem) {
      localProductSpuId.value = localItem.spu.id;
      favoriteStore.addToWishlist({
        id: 0,
        spu: {
          id: localProductSpuId.value,
        },
      });
      useNuxtApp().$showNotify({ msg: t('notify.addedToWishlist'), type: 'positive' });
    }
  } catch (error) {
    console.error('添加到收藏夹失败:', error);
    useNuxtApp().$showNotify({ msg: t('notify.addToWishlistFailed'), type: 'negative' });
  }
}

//
function removeFromWishlist() {
  try {
    console.log('移除收藏夹');
    if (localProductSpuId.value) {
      favoriteStore.removeFromWishlist(localProductSpuId.value);
      useNuxtApp().$showNotify({ msg: '移除成功', type: 'positive' });
    }
  } catch (error) {
    console.error('从收藏夹移除失败:', error);
    useNuxtApp().$showNotify({ msg: '从收藏夹移除失败', type: 'negative' });
  }
}

// 根据商品信息推断尺码助手类别
const getSizeGuideCategory = () => {
  if (!product) return 'women-top'; // 默认值

  const title = product.title?.toLowerCase() || '';
  const categoryName = product.categoryName?.toLowerCase() || '';

  // 根据商品标题和分类推断类型
  if (title.includes('鞋') || title.includes('shoe') || categoryName.includes('鞋') || categoryName.includes('shoe')) {
    // 根据性别判断
    if (title.includes('男') || title.includes('men') || categoryName.includes('男') || categoryName.includes('men')) {
      return 'men-shoes';
    } else {
      return 'women-shoes';
    }
  } else if (title.includes('裤') || title.includes('pants') || title.includes('trouser') || categoryName.includes('裤') || categoryName.includes('pants')) {
    // 裤子类
    if (title.includes('男') || title.includes('men') || categoryName.includes('男') || categoryName.includes('men')) {
      return 'men-pants';
    } else {
      return 'women-pants';
    }
  } else if (title.includes('戒指') || title.includes('ring') || categoryName.includes('戒指') || categoryName.includes('ring')) {
    return 'ring';
  } else {
    // 默认为上衣类
    if (title.includes('男') || title.includes('men') || categoryName.includes('男') || categoryName.includes('men')) {
      return 'men-top';
    } else {
      return 'women-top';
    }
  }
};

// 重试搜索
const retrySearch = async () => {
  if (id && platform) {
    await searchStore.searchProductDetailById(id, platform);
    if (searchStore.productDetail) {
      Object.assign(product, ProductApi.convertBackendProductToTemplate(searchStore.productDetail));
      cartPrice.value = product.price;
    }
  }
};

// 返回上一页
const goBack = () => {
  router.back();
};

// 跳转到自定义订单页面并预填充当前商品链接
const goToCustomOrderWithCurrentLink = () => {
  if (product && product.sourceLink) {
    const params = new URLSearchParams();
    params.set('productLink', product.sourceLink);
    router.push(`/diy-order?${params.toString()}`);
  } else {
    router.push('/diy-order');
  }
};

// 获取属性提示信息
const getPropertyHint = (propertyName) => {
  const hints = {
    颜色分类: '请根据您的喜好选择颜色，不同颜色可能会影响价格',
    适用床尺寸: '请根据您的床铺尺寸选择合适的规格',
    尺码: '建议参考尺码表选择合适的尺寸',
    材质: '不同材质具有不同的特性和手感',
    款式: '请选择您喜欢的款式设计',
  };
  return hints[propertyName] || '';
};

// 根据选项数量和屏幕尺寸计算网格类名
const getGridClass = (optionCount) => {
  const { isMobile } = useResponsive();

  if (isMobile.value) {
    // 移动端：最多2列，最少1列
    if (optionCount === 1) {
      return 'mobile-grid-1';
    } else {
      return 'mobile-grid-2';
    }
  } else {
    // PC端：最多3列，最少2列
    if (optionCount === 1) {
      return 'desktop-grid-2'; // 单个选项也用2列布局，居左显示
    } else if (optionCount === 2) {
      return 'desktop-grid-2';
    } else {
      return 'desktop-grid-3';
    }
  }
};
</script>

<style lang="scss" scoped>
.product-detail {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;

  .product-title {
    font-size: 20px;
    font-weight: bold;
    line-height: 1.3;
    color: #333;
    margin-top: 0;
  }

  .carousel-container {
    position: relative;
    border: 1px solid #eee;
    background-color: #fff;

    &.mobile-carousel-container {
      width: 100%;
      padding-top: 100%; /* 创建1:1的宽高比（方形） */
      position: relative;
      overflow: hidden;
      border-radius: 4px;
    }

    .mobile-carousel {
      position: absolute !important;
      top: 0;
      left: 0;
      width: 100%;
      height: 100% !important;

      .mobile-carousel-slide {
        padding: 0 !important;
      }

      .mobile-carousel-img {
        height: 100%;
        width: 100%;
      }
    }

    .mobile-carousel-nav {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      z-index: 10;
      background: rgba(0, 0, 0, 0.3);
      color: white;
      width: 36px;
      height: 36px;

      &-left {
        left: 10px;
      }

      &-right {
        right: 10px;
      }
    }
  }

  .thumbnail-carousel {
    .thumbnail-item {
      width: 80px;
      height: 80px;
      margin-right: 5px;
      cursor: pointer;
      border: 2px solid transparent;
      transition: all 0.3s ease;
      object-fit: contain;
      background-color: #fff;

      &.active {
        border-color: #1976d2; /* 选中高亮边框 */
        transform: scale(1.05);
      }

      &:hover {
        border-color: #64b5f6;
      }
    }
  }

  .thumbnail-carousel-mobile {
    .thumbnail-item-mobile {
      width: 60px;
      height: 60px;
      cursor: pointer;
      border: 2px solid transparent;
      transition: all 0.3s ease;
      object-fit: contain;
      background-color: #fff;

      &.active {
        border-color: #1976d2; /* 选中高亮边框 */
      }
    }
  }

  .price-section {
    padding: 8px 0;
    background: linear-gradient(to right, #f9f9f9, #f0f0f0);
    // border-left: 3px solid #1976d2;
    // box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

    .spec-label {
      color: #333;
      font-weight: 600;
      font-size: 15px;
      min-width: 70px;
    }

    .text-h5 {
      font-weight: 700;
      letter-spacing: -0.3px;
      font-size: 1.5rem;
    }
  }

  .product-info-container {
    .info-chip {
      display: flex;
      align-items: center;
      background-color: #f5f5f5;
      border-radius: 4px;
      padding: 6px 8px;
      font-size: 14px;

      .label-text {
        color: #555;
        font-weight: 500;
      }

      .value-text {
        color: #333;
        font-weight: 600;
      }
    }
  }
}

.specs-section {
  .label-text {
    color: #555;
    font-weight: 500;
    line-height: 1.4;
    font-size: 14px;
  }

  .spec-item {
    position: relative;
    padding-bottom: 10px;
    margin-bottom: 10px;
    border-bottom: 1px dashed #eee;

    &:last-child {
      border-bottom: none;
    }
  }

  .spec-label {
    color: #333;
    font-weight: 600;
    font-size: 15px;
    min-width: 70px;
  }
}

// 新的网格布局样式
.attribute-grid {
  display: grid;
  gap: 8px;
  margin: 8px 0;
  width: 100%;

  // PC端网格布局
  &.desktop-grid-2 {
    grid-template-columns: repeat(2, 1fr);
    max-width: 100%;
  }

  &.desktop-grid-3 {
    grid-template-columns: repeat(3, 1fr);
    max-width: 100%;
  }

  // 移动端网格布局
  &.mobile-grid-1 {
    grid-template-columns: 1fr;
    max-width: 100%;
  }

  &.mobile-grid-2 {
    grid-template-columns: repeat(2, 1fr);
    max-width: 100%;
  }

  .attribute-btn {
    min-width: 0; // 允许按钮收缩
    width: 100%; // 占满网格单元格
    height: 36px; // 单行文字的合适高度
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.2s ease;
    padding: 0 8px;
    white-space: nowrap; // 单行显示
    overflow: hidden; // 隐藏溢出
    text-overflow: ellipsis; // 用省略号表示截断
    display: flex;
    align-items: center;
    justify-content: flex-start; // 左对齐
    line-height: 1;
    text-align: left; // 文字左对齐

    &.long-text {
      font-size: 12px;
      padding: 0 6px;
    }

    &.selected-attribute {
      transform: scale(1.02);
      box-shadow: 0 2px 8px rgba(25, 118, 210, 0.3);
      border: 2px solid #1976d2;
    }

    &:hover:not(.disable) {
      transform: translateY(-1px);
      box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
    }

    &.disable {
      background: #f5f5f5 !important;
      border: 1px dashed #bdbdbd;
      opacity: 0.6;
      cursor: not-allowed;

      &:hover {
        transform: none;
        box-shadow: none;
      }
    }

    // 确保按钮内容左对齐且单行显示
    :deep(.q-btn__content) {
      line-height: 1;
      text-align: left; // 左对齐
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 100%;
      justify-content: flex-start; // 内容左对齐
    }
  }
}

// 保留原有的attribute-box样式以兼容其他地方
.attribute-box {
  margin: 3px 0;
  flex-wrap: wrap;

  .attribute-btn {
    margin: 3px 3px 3px 0;
    min-width: 70px;
    height: 32px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;

    &.selected-attribute {
      transform: scale(1.03);
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.15);
    }

    &:hover:not(.disable) {
      transform: translateY(-1px);
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    &.disable {
      background: #f5f5f5 !important;
      border: 1px dashed #bdbdbd;
      opacity: 0.7;
    }
  }
}

.quantity-selector {
  display: flex;
  align-items: center;

  .q-btn {
    border-radius: 50%;
    background-color: rgba(25, 118, 210, 0.1);
    width: 32px;
    height: 32px;

    &:hover {
      background-color: rgba(25, 118, 210, 0.2);
    }
  }

  .quantity-input {
    width: 65px;
    margin: 0 8px;

    :deep(.q-field__native) {
      text-align: center;
      font-weight: bold;
    }

    :deep(.q-field__control) {
      border-radius: 4px;
      height: 32px;
    }
  }
}

.mobile-center {
  @media (max-width: 1023px) {
    justify-content: center;
    text-align: center;
  }

  @media (min-width: 1024px) {
    justify-content: flex-start;
    text-align: left;
  }
}

.memo-container {
  display: flex;

  @media (max-width: 1023px) {
    justify-content: center;
  }

  @media (min-width: 1024px) {
    justify-content: flex-start;
  }
}

.memo-input {
  width: 100%;
  max-width: 100%;

  @media (max-width: 599px) {
    max-width: 90%;
  }

  :deep(.q-field__control) {
    border-radius: 4px;
  }
}

.spec-label {
  @media (max-width: 1023px) {
    text-align: center;
    margin: 0 auto;
  }

  @media (min-width: 1024px) {
    text-align: left;
    margin: 0;
  }
}

.action-buttons {
  margin-top: 20px;

  .action-btn {
    min-width: 130px;
    border-radius: 4px;
    font-weight: 500;
    letter-spacing: 0.3px;
    font-size: 14px;
    height: 36px;
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    }

    &.cart-btn {
      background: linear-gradient(135deg, #1976d2, #0d47a1);
    }

    &.buy-btn {
      background: linear-gradient(135deg, #ff5722, #e64a19);
    }

    &.wishlist-btn {
      &:hover {
        background: rgba(0, 0, 0, 0.05);
      }
    }
  }
}

.product-tabs {
  margin-top: 50px;

  .product-description {
    padding: 20px 0;

    :deep(img) {
      max-width: 100%;
      height: auto;
      display: block; // 消除图片下方的空隙
      margin: 0; // 移除默认边距
      padding: 0; // 移除默认内边距
    }

    // 移除所有可能产生间隙的元素
    :deep(p) {
      margin: 0;
      padding: 0;
      line-height: 0; // 移除行高产生的间隙
    }

    :deep(div) {
      margin: 0;
      padding: 0;
      line-height: 0;
    }

    // 移除所有文本节点可能产生的空白
    :deep(*) {
      font-size: 0; // 将字体大小设为0，消除空白字符产生的间隙
    }
  }
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
  text-align: center;

  h3 {
    margin: 20px 0 10px;
    color: #333;
  }

  p {
    margin-bottom: 20px;
    color: #666;
  }

  .error-actions {
    margin-bottom: 20px;
  }

  .custom-order-suggestion {
    width: 100%;
    max-width: 400px;

    .suggestion-card {
      border: 2px dashed #0073e6;

      h4 {
        color: #333;
        margin: 0;
      }

      p {
        color: #666;
        margin: 10px 0;
        line-height: 1.5;
      }
    }
  }
}

// 响应式样式
@media (max-width: 1023px) {
  .product-detail {
    .product-title {
      font-size: 18px;
    }

    .action-buttons {
      .q-btn {
        min-width: 120px;
      }
    }
  }
}

@media (max-width: 599px) {
  .product-detail {
    padding: 0 10px;

    .product-title {
      font-size: 16px;
      margin-top: 10px;
      text-align: center;
    }

    .price-section {
      padding: 8px 0;
      margin: 0 -10px;
      border-radius: 0;
      border-left: none;
      border-top: 3px solid #1976d2;
      background: linear-gradient(to bottom, #f9f9f9, #f0f0f0);

      .spec-label {
        font-size: 14px;
        color: #1976d2;
        margin-bottom: 8px;
      }
      .text-h5 {
        font-size: 1.4rem;
      }
    }

    .product-info-container {
      .info-chip {
        padding: 4px 8px;
        font-size: 13px;
      }
    }

    .specs-section {
      .spec-item {
        border-bottom: 1px solid #eee;
        padding-bottom: 12px;
        margin-bottom: 12px;
      }

      .spec-label {
        font-size: 14px;
        color: #1976d2;
        margin-bottom: 8px;
      }

      // 移动端网格布局优化
      .attribute-grid {
        gap: 6px;
        margin: 8px 0;
        width: 100%;

        &.mobile-grid-1 {
          max-width: 100%;
        }

        &.mobile-grid-2 {
          max-width: 100%;
        }

        .attribute-btn {
          min-width: 0;
          width: 100%;
          height: 32px;
          font-size: 12px;
          padding: 0 6px;
          border-radius: 4px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          line-height: 1;
          justify-content: flex-start; // 左对齐
          text-align: left; // 文字左对齐

          &.long-text {
            font-size: 11px;
            padding: 0 4px;
          }

          &.selected-attribute {
            transform: scale(1.01);
            box-shadow: 0 1px 6px rgba(25, 118, 210, 0.25);
          }

          // 移动端按钮内容样式
          :deep(.q-btn__content) {
            line-height: 1;
            text-align: left; // 左对齐
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 100%;
            justify-content: flex-start; // 内容左对齐
          }
        }
      }

      // 保留原有样式兼容性
      .attribute-box {
        .attribute-btn {
          min-width: auto;
          padding: 0 12px;
          height: 30px;
          margin: 3px;
          border-radius: 4px;
          font-size: 13px;
        }
      }
    }

    .quantity-selector {
      display: flex;
      justify-content: center;

      .q-btn {
        width: 30px;
        height: 30px;
      }

      .quantity-input {
        width: 50px;

        :deep(.q-field__control) {
          height: 30px;
        }
      }
    }

    .action-buttons {
      margin-top: 15px;

      .q-btn {
        width: 100%;
        margin-bottom: 8px;
        border-radius: 6px;
        height: 38px;
        font-size: 14px;
      }
    }

    .product-tabs {
      margin-top: 20px;
    }

    // 尺码助手相关样式
    .size-guide-container {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin-top: 8px;

      .size-guide-btn {
        font-size: 12px;
        padding: 4px 8px;
        min-height: auto;

        .q-btn__content {
          gap: 4px;
        }
      }
    }

    // 移动端调整
    @media (max-width: 599px) {
      .size-guide-container {
        justify-content: center;
        margin-top: 12px;

        .size-guide-btn {
          font-size: 13px;
          padding: 6px 12px;
        }
      }
    }

    .size-guide-dialog {
      .q-card__section {
        padding: 16px;
      }

      .size-guide-content-wrapper {
        max-height: calc(75vh - 100px);
        overflow-y: auto;
        padding: 0;
      }

      // 桌面端弹窗样式
      @media (min-width: 600px) {
        border-radius: 8px;

        .size-guide-content-wrapper {
          max-height: calc(700px - 100px);
          min-height: 400px;
        }
      }
    }
  }
}

// Tooltip 样式 - 支持多行显示
:deep(.q-tooltip) {
  max-width: 300px;
  white-space: normal;
  word-wrap: break-word;
  line-height: 1.4;
  padding: 8px 12px;

  @media (max-width: 768px) {
    max-width: 250px;
    font-size: 12px;
  }
}
</style>
